#!/bin/bash
#
# Yodoo Module Validation Script v2.0
# Validates Yodoo modules installation in Odoo 17 Docker environment
# Updated for cbmsmodules Docker setup with proper environment configuration
#

# --- ODOO 17 ENVIRONMENT CONFIGURATION ---
# Docker container names
ODOO_CONTAINER="cbmsmodules-odoo-1"
DB_CONTAINER="cbmsmodules-db-1"

# Odoo configuration
ODOO_DB_NAME="odoo17_test"
ODOO_DB_HOST="db"
ODOO_DB_PORT="5432"
ODOO_DB_USER="odoo"
ODOO_DB_PASSWORD="odoo"
ODOO_WEB_PORT="8070"
ODOO_LOG_PATH="./logs/odoo.log"

# Addons paths in container
ADDONS_PATH="/mnt/extra-addons,/mnt/extra-addons/generic-addons-temp,/mnt/extra-addons/crnd-web,/usr/lib/python3/dist-packages/odoo/addons"

# Yodoo modules to validate
YODOO_MODULES=("yodoo_apps_database" "yodoo_apps_download" "yodoo_github_connector" "yodoo_apps_website" "yodoo_apps_sale")

# Environment validation flags
VALIDATE_DOCKER=true
VALIDATE_DEPENDENCIES=true
VALIDATE_DATABASE=true
VALIDATE_MODULES=true
# --- END CONFIGURATION ---

# Function to print colored output
print_success() { echo -e "\033[32m✅ $1\033[0m"; }
print_error() { echo -e "\033[31m❌ $1\033[0m"; }
print_warning() { echo -e "\033[33m⚠️  $1\033[0m"; }
print_info() { echo -e "\033[34mℹ️  $1\033[0m"; }

# Function to validate Docker environment
validate_docker_environment() {
    echo "================================================="
    echo "🐳 DOCKER ENVIRONMENT VALIDATION"
    echo "================================================="

    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        print_error "Docker is not running or not accessible"
        return 1
    fi
    print_success "Docker is running"

    # Check if containers exist and are running
    if ! docker ps --format "table {{.Names}}" | grep -q "$ODOO_CONTAINER"; then
        print_error "Odoo container '$ODOO_CONTAINER' is not running"
        return 1
    fi
    print_success "Odoo container '$ODOO_CONTAINER' is running"

    if ! docker ps --format "table {{.Names}}" | grep -q "$DB_CONTAINER"; then
        print_error "Database container '$DB_CONTAINER' is not running"
        return 1
    fi
    print_success "Database container '$DB_CONTAINER' is running"

    # Check if Odoo web interface is accessible
    if curl -s -o /dev/null -w "%{http_code}" "http://localhost:$ODOO_WEB_PORT" | grep -q "200"; then
        print_success "Odoo web interface is accessible on port $ODOO_WEB_PORT"
    else
        print_warning "Odoo web interface may not be fully ready on port $ODOO_WEB_PORT"
    fi

    return 0
}

# Function to validate dependencies
validate_dependencies() {
    echo "================================================="
    echo "📦 DEPENDENCIES VALIDATION"
    echo "================================================="

    # Check if Git is installed in container
    if docker exec "$ODOO_CONTAINER" which git >/dev/null 2>&1; then
        print_success "Git is installed in Odoo container"
        GIT_VERSION=$(docker exec "$ODOO_CONTAINER" git --version)
        print_info "Git version: $GIT_VERSION"
    else
        print_error "Git is not installed in Odoo container"
        return 1
    fi

    # Check if GitPython is installed
    if docker exec "$ODOO_CONTAINER" python3 -c "import git; print('GitPython version:', git.__version__)" 2>/dev/null; then
        print_success "GitPython is installed and accessible"
    else
        print_error "GitPython is not installed or not accessible"
        return 1
    fi

    # Check if CRND dependencies are available (using double slash to avoid Git Bash path conversion)
    if docker exec "$ODOO_CONTAINER" ls //mnt/extra-addons/generic-addons-temp >/dev/null 2>&1; then
        print_success "CRND generic-addons dependencies are available"
    else
        print_error "CRND generic-addons dependencies are missing"
        return 1
    fi

    if docker exec "$ODOO_CONTAINER" ls //mnt/extra-addons/crnd-web >/dev/null 2>&1; then
        print_success "CRND web dependencies are available"
    else
        print_error "CRND web dependencies are missing"
        return 1
    fi

    return 0
}

# Function to validate database connection
validate_database() {
    echo "================================================="
    echo "🗄️  DATABASE VALIDATION"
    echo "================================================="

    # Check if database exists and is accessible
    if docker exec "$DB_CONTAINER" psql -U "$ODOO_DB_USER" -d "$ODOO_DB_NAME" -c "SELECT version();" >/dev/null 2>&1; then
        print_success "Database '$ODOO_DB_NAME' is accessible"
        DB_VERSION=$(docker exec "$DB_CONTAINER" psql -U "$ODOO_DB_USER" -d "$ODOO_DB_NAME" -t -c "SELECT version();" | head -1 | xargs)
        print_info "Database version: $DB_VERSION"
    else
        print_error "Cannot connect to database '$ODOO_DB_NAME'"
        return 1
    fi

    # Check if Odoo tables exist (indicating database is initialized)
    if docker exec "$DB_CONTAINER" psql -U "$ODOO_DB_USER" -d "$ODOO_DB_NAME" -c "SELECT COUNT(*) FROM ir_module_module;" >/dev/null 2>&1; then
        print_success "Odoo database is properly initialized"
        MODULE_COUNT=$(docker exec "$DB_CONTAINER" psql -U "$ODOO_DB_USER" -d "$ODOO_DB_NAME" -t -c "SELECT COUNT(*) FROM ir_module_module;" | xargs)
        print_info "Total modules in database: $MODULE_COUNT"
    else
        print_error "Odoo database is not properly initialized"
        return 1
    fi

    return 0
}

# Function to get module status from database
get_module_status() {
    local module_name=$1
    docker exec "$ODOO_CONTAINER" python3 -c "
import sys
sys.path.append('/usr/lib/python3/dist-packages')
import odoo
from odoo import SUPERUSER_ID
from odoo.tools import config

# Configure Odoo
config['db_host'] = '$ODOO_DB_HOST'
config['db_port'] = $ODOO_DB_PORT
config['db_user'] = '$ODOO_DB_USER'
config['db_password'] = '$ODOO_DB_PASSWORD'
config['addons_path'] = '$ADDONS_PATH'

try:
    registry = odoo.registry('$ODOO_DB_NAME')
    with registry.cursor() as cr:
        env = odoo.api.Environment(cr, SUPERUSER_ID, {})
        module = env['ir.module.module'].search([('name', '=', '$module_name')], limit=1)
        if module:
            print(module.state)
        else:
            print('not_found')
except Exception as e:
    print('error')
" 2>/dev/null
}

# Function to validate individual module
validate_module() {
    local module_name=$1
    echo "================================================="
    echo "🔧 VALIDATING MODULE: $module_name"
    echo "================================================="

    # Check if module directory exists (using double slash to avoid Git Bash path conversion)
    if docker exec "$ODOO_CONTAINER" ls "//mnt/extra-addons/$module_name" >/dev/null 2>&1; then
        print_success "Module directory exists: /mnt/extra-addons/$module_name"
    else
        print_error "Module directory not found: /mnt/extra-addons/$module_name"
        return 1
    fi

    # Check if manifest file exists
    if docker exec "$ODOO_CONTAINER" ls "//mnt/extra-addons/$module_name/__manifest__.py" >/dev/null 2>&1; then
        print_success "Module manifest file exists"
    else
        print_error "Module manifest file not found"
        return 1
    fi

    # Get module status from database
    print_info "Checking module status in database..."
    MODULE_STATUS=$(get_module_status "$module_name")

    case "$MODULE_STATUS" in
        "installed")
            print_success "Module '$module_name' is installed and active"
            ;;
        "uninstalled")
            print_warning "Module '$module_name' is available but not installed"
            ;;
        "to install")
            print_info "Module '$module_name' is marked for installation"
            ;;
        "to upgrade")
            print_info "Module '$module_name' is marked for upgrade"
            ;;
        "not_found")
            print_warning "Module '$module_name' not found in database (may need to update apps list)"
            ;;
        "error")
            print_error "Error checking module '$module_name' status"
            return 1
            ;;
        *)
            print_warning "Module '$module_name' has unknown status: $MODULE_STATUS"
            ;;
    esac

    return 0
}

# Function to install/upgrade a module
install_module() {
    local module_name=$1
    local action=$2  # "install" or "upgrade"

    echo "================================================="
    echo "🚀 ${action^^}ING MODULE: $module_name"
    echo "================================================="

    local flag
    if [ "$action" = "install" ]; then
        flag="-i"
    else
        flag="-u"
    fi

    print_info "Executing: odoo $flag $module_name --stop-after-init"

    # Execute the installation/upgrade command
    local install_output
    install_output=$(docker exec "$ODOO_CONTAINER" bash -c "
        odoo --database=$ODOO_DB_NAME \
             --db_host=$ODOO_DB_HOST \
             --db_port=$ODOO_DB_PORT \
             --db_user=$ODOO_DB_USER \
             --db_password=$ODOO_DB_PASSWORD \
             --addons-path='$ADDONS_PATH' \
             $flag $module_name \
             --stop-after-init \
             --without-demo=all \
             --logfile=/dev/stdout
    " 2>&1)

    local exit_code=$?

    # Check for success/failure indicators
    if [ $exit_code -eq 0 ] && echo "$install_output" | grep -q "Registry loaded"; then
        print_success "Module '$module_name' ${action}ed successfully"
        return 0
    else
        print_error "Failed to $action module '$module_name'"
        echo "--- Installation Output ---"
        echo "$install_output" | tail -20
        echo "----------------------------"
        return 1
    fi
}

# Function to run comprehensive validation
run_full_validation() {
    echo "================================================="
    echo "🔍 YODOO MODULES COMPREHENSIVE VALIDATION"
    echo "================================================="
    echo "Environment: Odoo 17 Docker Setup"
    echo "Database: $ODOO_DB_NAME"
    echo "Web Interface: http://localhost:$ODOO_WEB_PORT"
    echo "================================================="

    local validation_failed=false

    # Step 1: Validate Docker environment
    if [ "$VALIDATE_DOCKER" = true ]; then
        if ! validate_docker_environment; then
            validation_failed=true
        fi
    fi

    # Step 2: Validate dependencies
    if [ "$VALIDATE_DEPENDENCIES" = true ]; then
        if ! validate_dependencies; then
            validation_failed=true
        fi
    fi

    # Step 3: Validate database
    if [ "$VALIDATE_DATABASE" = true ]; then
        if ! validate_database; then
            validation_failed=true
        fi
    fi

    # Step 4: Validate all Yodoo modules
    if [ "$VALIDATE_MODULES" = true ]; then
        for module in "${YODOO_MODULES[@]}"; do
            if ! validate_module "$module"; then
                validation_failed=true
            fi
        done
    fi

    # Final summary
    echo "================================================="
    echo "📋 VALIDATION SUMMARY"
    echo "================================================="

    if [ "$validation_failed" = true ]; then
        print_error "VALIDATION FAILED: Some components have issues"
        echo ""
        echo "🔧 TROUBLESHOOTING STEPS:"
        echo "1. Ensure Docker containers are running: docker-compose up -d"
        echo "2. Check container logs: docker logs $ODOO_CONTAINER"
        echo "3. Verify database connection: docker exec $DB_CONTAINER psql -U $ODOO_DB_USER -d $ODOO_DB_NAME -c 'SELECT 1;'"
        echo "4. Access Odoo web interface: http://localhost:$ODOO_WEB_PORT"
        echo "5. Install modules manually through Apps menu if needed"
        return 1
    else
        print_success "ALL VALIDATIONS PASSED"
        echo ""
        echo "🎉 ENVIRONMENT STATUS:"
        echo "✅ Docker containers are running"
        echo "✅ Dependencies are installed"
        echo "✅ Database is accessible"
        echo "✅ All Yodoo modules are available"
        echo ""
        echo "🌐 Access your Odoo instance at: http://localhost:$ODOO_WEB_PORT"
        echo "📚 Available Yodoo modules: ${YODOO_MODULES[*]}"
        return 0
    fi
}

# Main script execution
main() {
    # Check command line arguments
    case "${1:-}" in
        "")
            # No arguments - run full validation
            run_full_validation
            ;;
        "--help"|"-h")
            echo "Yodoo Module Validation Script v2.0"
            echo ""
            echo "Usage:"
            echo "  $0                    # Run full environment validation"
            echo "  $0 <module_name>      # Validate specific module"
            echo "  $0 install <module>   # Install specific module"
            echo "  $0 upgrade <module>   # Upgrade specific module"
            echo "  $0 --help             # Show this help"
            echo ""
            echo "Available Yodoo modules: ${YODOO_MODULES[*]}"
            ;;
        "install")
            if [ -z "$2" ]; then
                print_error "Module name required for installation"
                echo "Usage: $0 install <module_name>"
                exit 1
            fi
            validate_module "$2" && install_module "$2" "install"
            ;;
        "upgrade")
            if [ -z "$2" ]; then
                print_error "Module name required for upgrade"
                echo "Usage: $0 upgrade <module_name>"
                exit 1
            fi
            validate_module "$2" && install_module "$2" "upgrade"
            ;;
        *)
            # Single module validation
            validate_module "$1"
            ;;
    esac
}

# Execute main function with all arguments
main "$@"