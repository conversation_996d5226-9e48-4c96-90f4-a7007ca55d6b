<?xml version="1.0" encoding="UTF-8"?>
<odoo>
    <record id="res_config_settings_view_form" model="ir.ui.view">
        <field name="model">res.config.settings</field>
        <field name="priority" eval="20"/>
        <field name="inherit_id" ref="base_setup.res_config_settings_view_form"/>
        <field name="arch" type="xml">
            <xpath expr="//form" position="inside">
                <app data-string="Yodoo Apps" string="Yodoo Apps" name="yodoo_apps_database"
                     groups="base.group_system">
                    <block title="Yodoo Apps Configuration" name="yodoo_apps_settings">

                        <div class="row mt16 o_settings_container" name="yodoo_apps_configuration">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane"/>
                                <div class="o_setting_right_pane">
                                    <label for="yodoo_select_last_version_as"
                                           string="Select Last Version As"
                                           class="o_light_label"/>
                                    <field name="yodoo_select_last_version_as"/>
                                    <div class="text-muted">
                                        Choose the way to select last version of module or module serie.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </block>
                    <block title="Install Yodoo Apps Features" name="yodoo_apps_install_features">
                        <div class="row mt16 o_settings_container" name="yodoo_apps_install_features">
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="module_yodoo_apps_assembly"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_yodoo_apps_assembly" class="o_light_label"/>
                                    <div class="text-muted">
                                         Manage addons packs via Gitlab.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="module_yodoo_apps_sale"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_yodoo_apps_sale" class="o_light_label"/>
                                    <div class="text-muted">
                                        Convert yodoo modules to products and sale them.
                                    </div>
                                </div>
                            </div>
                            <div class="col-12 col-lg-6 o_setting_box">
                                <div class="o_setting_left_pane">
                                    <field name="module_yodoo_apps_assembly_sale"/>
                                </div>
                                <div class="o_setting_right_pane">
                                    <label for="module_yodoo_apps_assembly_sale" class="o_light_label"/>
                                    <div class="text-muted">
                                        Convert yodoo assemblies to products and sale them.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </block>
                </app>
            </xpath>
        </field>
    </record>

    <record id="action_yodoo_apps_settings" model="ir.actions.act_window">
        <field name="name">Yodoo Apps Settings</field>
        <field name="type">ir.actions.act_window</field>
        <field name="res_model">res.config.settings</field>
        <field name="view_mode">form</field>
        <field name="target">inline</field>
        <field name="context">{'module' : 'yodoo_apps_database'}</field>
    </record>

    <menuitem id="yodoo_apps_settings" name="Settings"
              parent="yodoo_apps_database.menu_root_settings"
              sequence="10"
              action="action_yodoo_apps_settings"
              groups="base.group_system"/>
</odoo>
