{
    'name': "Yodoo Apps Download",
    'summary': """Secure download system for purchased Yodoo modules""",
    'author': "Center of Research and Development",
    'website': "https://crnd.pro",
    'category': 'Yodoo Apps',
    'version': '********.0',
    'depends': [
        'sale',
        'portal',
        'yodoo_apps_database',
        'yodoo_apps_sale',
        'yodoo_apps_website',
        'yodoo_github_connector',
    ],
    # Note: zipfile, tempfile, and secrets are Python standard library modules
    # and don't need to be listed as external dependencies
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'data/ir_sequence.xml',
        'data/ir_cron.xml',
        'views/yodoo_module_purchase_views.xml',
        'views/yodoo_module_download_views.xml',
        'views/sale_order_views.xml',
        'views/menu.xml',
        'templates/portal_downloads.xml',
        'templates/download_success.xml',
        'templates/download_error.xml',
        'templates/license_info.xml',
        'templates/installation_guide.xml',
        'templates/email_download_ready.xml',
        'templates/email_license_key.xml',
    ],
    'demo': [
        # Temporarily disabled demo data to avoid installation issues
        # 'demo/purchase_demo.xml',
        # 'demo/download_demo.xml',
    ],
    'images': ['static/description/banner.png'],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
