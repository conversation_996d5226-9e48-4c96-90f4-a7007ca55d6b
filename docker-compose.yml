services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: odoo
      POSTGRES_PASSWORD: odoo
    volumes:
      - odoo-db-data:/var/lib/postgresql/data
    ports:
      - "5434:5432"
    networks:
      - odoo-network

  odoo:
    image: odoo:17.0
    depends_on:
      - db
    user: root
    ports:
      - "8070:8069"
    environment:
      HOST: db
      USER: odoo
      PASSWORD: odoo
    volumes:
      - odoo-web-data:/var/lib/odoo
      - ./config:/etc/odoo
      - ./testrepo:/mnt/extra-addons
      - ./logs:/var/log/odoo
    networks:
      - odoo-network
    command: >
      bash -c "apt-get update && apt-get install -y git && pip install gitpython &&
      odoo
      --database=odoo17_test
      --db_host=db
      --db_port=5432
      --db_user=odoo
      --db_password=odoo
      --addons-path=/mnt/extra-addons,/mnt/extra-addons/generic-addons-temp,/mnt/extra-addons/crnd-web,/usr/lib/python3/dist-packages/odoo/addons
      --logfile=/var/log/odoo/odoo.log
      --log-level=info
      --without-demo=all"

volumes:
  odoo-web-data:
  odoo-db-data:

networks:
  odoo-network:
    driver: bridge
